/**
 * 测试AI网关图片处理功能
 * 此脚本测试ai-gateway.js中的图片处理功能，包括URL图片转base64的功能
 * 支持多种格式的图片测试，包括本地图片和远程图片URL
 */

import { generateAiGatewayObject } from '../src/ai-providers/ai-gateway.js';
import { log } from './modules/utils.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { z } from 'zod';

// 获取当前文件的目录路径
// 注意：如果使用 CommonJS，请使用 __dirname 代替
// const __dirname = path.dirname(fileURLToPath(import.meta.url));

// 测试图片路径
// 假设 page.png 在项目根目录下
const LOCAL_PNG_PATH = path.join(process.cwd(), 'page.png');

// 测试图片URL（使用确定的PNG图片URL）
const TEST_PNG_IMAGE_URL =
	'https://upload.wikimedia.org/wikipedia/commons/4/47/PNG_transparency_demonstration_1.png';

/**
 * 测试带有图片的消息处理
 * @param {string} imageUrl - 要测试的图片URL
 * @param {string} imageType - 图片类型描述（用于日志）
 */
async function testImageProcessing(imageUrl, imageType) {
	log('info', `开始测试AI网关${imageType}图片处理功能...`);

	try {
		// 构建包含图片的消息
		const messages = [
			{
				role: 'user',
				content:
					'你是一个能够处理图片的AI助手。请描述图片内容，特别是图片中的文字。'
			},
			{
				role: 'user',
				content: [
					{
						type: 'text',
						text: '这个图片有哪些文字？要求回复要详细一些！'
					},
					{
						type: 'image_url',
						image_url: {
							url: imageUrl
						}
					}
				]
			}
		];

		log('info', `使用${imageType}图片URL: ${imageUrl}`);

		// 调用AI网关生成文本
		const result = await generateAiGatewayObject({
			modelId: 'claude-3-5-sonnet',
			messages,
			maxTokens: 1000,
			temperature: 0.7
		});

		// 输出结果
		log('info', `${imageType}测试成功！AI响应：`);
		log('info', result.text);
		log(
			'info',
			`输入tokens: ${result.usage.inputTokens}, 输出tokens: ${result.usage.outputTokens}`
		);

		return result;
	} catch (error) {
		log('error', `${imageType}测试失败: ${error.message}`);
		if (error.stack) {
			log('debug', error.stack);
		}
		throw error;
	}
}

/**
 * 测试所有图片格式
 */
/**
 * 读取本地图片并转换为base64格式
 * @param {string} imagePath - 图片路径
 * @returns {string} base64格式的图片数据
 */
function readLocalImageAsBase64(imagePath) {
	try {
		// 检查文件是否存在
		if (!fs.existsSync(imagePath)) {
			throw new Error(`图片文件不存在: ${imagePath}`);
		}

		// 读取图片文件
		const imageBuffer = fs.readFileSync(imagePath);

		// 确定MIME类型
		let mimeType = 'image/png';
		if (
			imagePath.toLowerCase().endsWith('.jpg') ||
			imagePath.toLowerCase().endsWith('.jpeg')
		) {
			mimeType = 'image/jpeg';
		} else if (imagePath.toLowerCase().endsWith('.gif')) {
			mimeType = 'image/gif';
		}

		// 转换为base64
		const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;

		return base64Image;
	} catch (error) {
		log('error', `读取本地图片失败: ${error.message}`);
		throw error;
	}
}

/**
 * 测试本地图片
 * @param {string} imagePath - 本地图片路径
 */
async function testLocalImage(imagePath) {
	log('info', `开始测试本地图片: ${imagePath}`);

	try {
		// 读取本地图片并转换为base64
		const base64Image = readLocalImageAsBase64(imagePath);

		// 构建包含图片的消息
		const messages = [
			{
				role: 'user',
				content:
					'你是一个能够处理图片的AI助手。请描述图片内容，特别是图片中的文字。'
			},
			{
				role: 'user',
				content: [
					{
						type: 'text',
						text: '这个图片有哪些文字？要求回复要详细一些！'
					},
					{
						type: 'image_url',
						image_url: {
							url: base64Image
						}
					}
				]
			}
		];
		// Define the Zod schema for a SINGLE task object
		const prdSingleTaskSchema = z.object({
			id: z.number().int().positive(),
			title: z.string().min(1),
			description: z.string().min(1),
			details: z.string().optional().default(''),
			testStrategy: z.string().optional().default(''),
			priority: z.enum(['high', 'medium', 'low']).default('medium'),
			dependencies: z.array(z.number().int().positive()).optional().default([]),
			status: z.string().optional().default('pending')
		});

		// Define the Zod schema for the ENTIRE expected AI response object
		const prdResponseSchema = z.object({
			tasks: z.array(prdSingleTaskSchema),
			metadata: z.object({
				projectName: z.string(),
				totalTasks: z.number(),
				sourceFile: z.string(),
				generatedAt: z.string()
			})
		});
		// 调用AI网关生成文本
		const result = await generateAiGatewayObject({
			modelId: 'claude-3-5-sonnet',
			messages,
			maxTokens: 1000,
			temperature: 0.7,
			objectName: 'text',
			schema: prdResponseSchema
		});

		// 输出结果
		log('info', `本地图片测试成功！AI响应：`);
		log('info', result.text);
		log(
			'info',
			`输入tokens: ${result.usage.inputTokens}, 输出tokens: ${result.usage.outputTokens}`
		);

		return result;
	} catch (error) {
		log('error', `本地图片测试失败: ${error.message}`);
		if (error.stack) {
			log('debug', error.stack);
		}
		throw error;
	}
}

async function testAllImageFormats() {
	try {
		// 测试本地PNG图片
		await testLocalImage(LOCAL_PNG_PATH);

		// 测试远程PNG图片
		// await testImageProcessing(TEST_PNG_IMAGE_URL, 'PNG');

		log('info', '所有图片格式测试完成');
	} catch (error) {
		log('error', `图片格式测试过程中发生错误: ${error.message}`);
	}
}

// 执行测试
testAllImageFormats()
	.then(() => {
		log('info', '所有测试完成');
	})
	.catch((error) => {
		log('error', `测试过程中发生错误: ${error.message}`);
	});
