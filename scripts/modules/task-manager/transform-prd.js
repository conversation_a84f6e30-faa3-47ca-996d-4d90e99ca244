/**
 * transform-prd.js
 * 将不规范的PRD转换为格式化的PRD
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import boxen from 'boxen';
import { z } from 'zod';

import {
	log,
	enableSilentMode,
	disableSilentMode,
	isSilentMode
} from '../utils.js';

import {
	generateObjectService,
	generateTextService
} from '../ai-services-unified.js';
import { getDebugFlag } from '../config-manager.js';

// 定义Zod schema，用于验证AI服务返回的结果
const transformPrdResponseSchema = z.object({
	text: z.string().min(1),
	missingInfo: z.array(z.string()).optional()
});

/**
 * 转换PRD文件为格式化的PRD
 * @param {string} inputPath - 输入PRD文件路径
 * @param {string} outputPath - 输出格式化PRD文件路径
 * @param {Object} options - 附加选项
 * @param {Object} [options.reportProgress] - 进度报告函数（可选）
 * @param {Object} [options.mcpLog] - MCP日志对象（可选）
 * @param {Object} [options.session] - MCP服务器会话对象（可选）
 * @param {string} [options.projectRoot] - 项目根目录路径（用于MCP/env回退）
 * @param {string} [outputFormat='text'] - 输出格式（'text'或'json'）
 * @returns {Promise<Object>} - 包含成功状态和数据/错误信息的结果对象
 */
async function transformPRD(inputPath, outputPath, options = {}) {
	const { reportProgress, mcpLog, session, projectRoot } = options;
	const isMCP = !!mcpLog;
	const outputFormat = isMCP ? 'json' : 'text';

	const logFn = mcpLog
		? mcpLog
		: {
				// CLI包装器
				info: (...args) => log('info', ...args),
				warn: (...args) => log('warn', ...args),
				error: (...args) => log('error', ...args),
				debug: (...args) => log('debug', ...args),
				success: (...args) => log('success', ...args)
			};

	// 创建自定义报告器
	const report = (message, level = 'info') => {
		// 直接检查logFn
		if (logFn && typeof logFn[level] === 'function') {
			logFn[level](message);
		} else if (!isSilentMode() && outputFormat === 'text') {
			// 仅在必要时回退到原始日志
			log(level, message);
		}
	};

	// 检查输入文件是否存在
	if (!fs.existsSync(inputPath)) {
		const errorMsg = `输入PRD文件未找到: ${inputPath}`;
		report(errorMsg, 'error');
		return {
			success: false,
			message: errorMsg
		};
	}

	// 确保输出文件与输入文件在同一目录
	const inputDir = path.dirname(inputPath);
	const inputExt = path.extname(inputPath);
	const inputBaseName = path.basename(inputPath, inputExt);

	// 如果未指定输出路径，则默认为同目录下的formatted-xxx.md
	const defaultOutputPath = path.join(
		inputDir,
		`formatted-${inputBaseName}${inputExt}`
	);
	const finalOutputPath = outputPath || defaultOutputPath;

	// 记录转换信息
	report(`转换PRD文件: ${inputPath} 到 ${finalOutputPath}`);

	// 确保输出目录存在
	const outputDir = path.dirname(finalOutputPath);
	try {
		if (!fs.existsSync(outputDir)) {
			report(`创建输出目录: ${outputDir}`, 'info');
			fs.mkdirSync(outputDir, { recursive: true });
		}
	} catch (dirError) {
		const errorMsg = `创建输出目录失败 ${outputDir}: ${dirError.message}`;
		report(errorMsg, 'error');
		return {
			success: false,
			message: errorMsg
		};
	}

	try {
		// 读取输入PRD文件
		const prdContent = fs.readFileSync(inputPath, 'utf8');

		// 准备AI服务的提示
		const prompt = `
			# 角色
			您是一位PRD格式化专家。您的任务是将非结构化的PRD文档转换为结构良好的格式。
			如果PRD包含UI原型图或描述，请将它们转换为结构化的XML描述。
			同时识别标准PRD中应包含但当前缺失的信息。

			# 输入
			以下是非结构化的PRD内容：
			
			${prdContent}
			
			# 输出期望
			请将其格式化为包含以下部分的结构化PRD, 仅输出markdown里的内容：
			\`\`\`
			# 项目概述
			# 功能需求
			# UI/UX需求（包含任何UI组件的XML描述）
			# 技术需求实现细节
			# 缺失信息列表
			\`\`\`
			
			# UI原型转XML指南
			对于任何UI原型图或描述，请分析并生成详细的XML组件树描述，该描述应包含界面的完整结构、样式、数据流和交互逻辑。

			## 组件结构识别
			- 首先识别页面的主要布局结构（如Header, Sidebar, Content, Footer）
			- 识别每个区域内的组件类型（如卡片、表格、图表、按钮、输入框等）
			- 识别组件的嵌套关系和层次结构

			## 样式属性提取
			为每个组件标记以下属性（适用时）：
			- 尺寸（宽度、高度、内边距、外边距）
			- 颜色（背景色、文字颜色、边框颜色、强调色）
			- 排版（字体大小、字重、行高、对齐方式）
			- 边框与阴影（边框宽度、样式、圆角、阴影效果）
			- 布局属性（flex、grid、对齐方式等）

			## 数据流绑定
			为数据展示组件添加：
			- 数据源指定（API路径或数据变量）
			- 刷新间隔（如适用）
			- 数据格式化器（如货币、日期等）

			## 交互行为定义
			为交互元素添加：
			- 事件处理器（onClick、onChange等）
			- 状态转换规则
			- 动画效果（如适用）

			## XML示例结构
			示例如下：
			\`\`\`xml
				<UI component="dashboard">
				<Header backgroundColor="#f5f5f5" height="60px">
					<Logo src="logo.png" width="120px" />
					<NavBar align="right">
					<NavItem label="首页" link="#home" active="true" />
					<NavItem label="产品" link="#products" />
					<NavItem label="设置" link="#settings" />
					</NavBar>
				</Header>
				
				<Sidebar width="240px" backgroundColor="#ffffff">
					<Menu>
					<MenuItem icon="dashboard" label="仪表盘" />
					<MenuItem icon="users" label="用户管理" />
					<MenuItem icon="settings" label="系统设置" />
					</Menu>
				</Sidebar>
				
				<Content padding="20px">
					<Row>
					<Card title="用户统计" width="300px">
						<Chart type="bar" dataSource="api/user-stats" />
					</Card>
					</Row>
				</Content>
				</UI>
			\`\`\`

			## 常见组件类型参考
			- 结构组件：Dashboard, Page, Header, Footer, Sidebar, Content, Row, Column, Panel, Card, TabPanel
			- 导航组件：NavBar, NavItem, NavGroup, Breadcrumb, Pagination, TabGroup, Tab
			- 表单组件：Form, Input, Select, Checkbox, Radio, Switch, DatePicker, Button
			- 数据展示：Table, List, ListItem, MetricCard, Description, Tag, Badge, Avatar
			- 图表组件：Chart, BarChart, LineChart, PieChart, DonutChart, AreaChart, ScatterChart
			- 交互组件：Modal, Drawer, Dropdown, Tooltip, Popover, Notification, Progress

			# 缺失信息识别
			请识别标准PRD中应包含但当前文档中缺失的任何信息，并在文档末尾以问题列表的形式列出。
			这些问题应该帮助用户补充PRD中的关键信息。
		`;

		const wasSilent = isSilentMode();
		if (!wasSilent) {
			enableSilentMode();
		}

		try {
			// 调用AI服务转换PRD
			const aiServiceResponse = await generateTextService({
				prompt,
				session,
				projectRoot,
				outputType: mcpLog ? 'mcp' : 'cli',
				commandName: 'transform-prd', // 命令名称
				role: 'main' // 使用主要模型角色
			});

			// 检查AI服务是否返回有效响应
			console.log('[DEBUG] AI服务响应:', JSON.stringify(aiServiceResponse, null, 2));
			if (!aiServiceResponse || !aiServiceResponse.mainResult) {
				const errorMsg = 'AI服务返回无效响应';
				report(errorMsg, 'error');
				return {
					success: false,
					message: errorMsg
				};
			}

			// 提取格式化的PRD内容
			const formattedPRD = aiServiceResponse.mainResult;

			// 将格式化的PRD写入输出文件
			try {
				// 使用console.log直接输出调试信息
				console.log(`[DEBUG] 尝试写入文件: ${finalOutputPath}`);
				console.log(`[DEBUG] 输出目录: ${path.dirname(finalOutputPath)}`);
				console.log(`[DEBUG] 输出目录是否存在: ${fs.existsSync(path.dirname(finalOutputPath))}`);
				console.log(`[DEBUG] 输出文件内容长度: ${formattedPRD.length}`);
				
				// 确保输出目录存在
				if (!fs.existsSync(path.dirname(finalOutputPath))) {
					console.log(`[DEBUG] 创建输出目录: ${path.dirname(finalOutputPath)}`);
					fs.mkdirSync(path.dirname(finalOutputPath), { recursive: true });
				}
				
				fs.writeFileSync(finalOutputPath, formattedPRD, 'utf8');
				console.log(`[DEBUG] 文件写入成功: ${finalOutputPath}`);
				console.log(`[DEBUG] 文件是否存在: ${fs.existsSync(finalOutputPath)}`);
				if (fs.existsSync(finalOutputPath)) {
					console.log(`[DEBUG] 文件大小: ${fs.statSync(finalOutputPath).size} 字节`);
				}
			} catch (writeError) {
				console.error(`[ERROR] 写入文件失败: ${writeError.message}`);
				console.error(writeError.stack);
				throw writeError;
			}
			const successMsg = `成功转换PRD并保存到 ${finalOutputPath}`;
			report(successMsg, 'success');

			// 提取缺失信息的问题
			const missingInfoRegex =
				/(?:缺失信息|需要补充的信息|问题列表)[:\s]*((?:(?:\d+\.?|\*|-)\s*[^\n]+\n?)+)/i;
			const missingInfoMatch = formattedPRD.match(missingInfoRegex);
			const missingInfo = missingInfoMatch ? missingInfoMatch[1].trim() : '';
			const missingInfoList = missingInfo
				? missingInfo
						.split('\n')
						.map((q) => q.trim())
						.filter((q) => q)
				: [];

			// 在CLI模式下显示结果
			if (outputFormat === 'text') {
				console.log(
					boxen(chalk.green(`成功转换PRD并保存到: ${finalOutputPath}`), {
						padding: 1,
						borderColor: 'green',
						borderStyle: 'round'
					})
				);

				if (missingInfoList.length > 0) {
					console.log(
						boxen(
							chalk.yellow.bold('需要补充的信息:') +
								'\n\n' +
								missingInfoList
									.map((q, i) => `${chalk.cyan(i + 1)}. ${q}`)
									.join('\n'),
							{
								padding: 1,
								borderColor: 'yellow',
								borderStyle: 'round',
								margin: { top: 1 }
							}
						)
					);
				}

				console.log(
					boxen(
						chalk.white.bold('下一步:') +
							'\n\n' +
							`${chalk.cyan('1.')} 运行 ${chalk.yellow('task-master parse-prd')} 基于格式化的PRD生成任务`,
						{
							padding: 1,
							borderColor: 'cyan',
							borderStyle: 'round',
							margin: { top: 1 }
						}
					)
				);

				if (aiServiceResponse && aiServiceResponse.telemetryData) {
					// 如果有显示AI使用摘要的函数，可以在这里调用
					// displayAiUsageSummary(aiServiceResponse.telemetryData, 'cli');
				}
			}

			return {
				success: true,
				outputPath: finalOutputPath,
				missingInfo: missingInfoList,
				telemetryData: aiServiceResponse.telemetryData || null
			};
		} finally {
			if (!wasSilent && isSilentMode()) {
				disableSilentMode();
			}
		}
	} catch (error) {
		const errorMsg = `转换PRD时出错: ${error.message}`;
		report(errorMsg, 'error');

		// 仅为文本输出（CLI）显示错误UI
		if (outputFormat === 'text') {
			console.error(chalk.red(`错误: ${error.message}`));

			if (getDebugFlag(projectRoot)) {
				// 使用projectRoot进行调试标志检查
				console.error(error);
			}

			process.exit(1);
		} else {
			return {
				success: false,
				message: errorMsg
			};
		}
	}
}

export default transformPRD;
