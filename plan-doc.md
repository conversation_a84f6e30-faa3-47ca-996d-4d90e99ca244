# 任务管理和执行 MCP 服务器设计方案

## 项目概述

基于现有的 claude-task-master 项目，在 `planner-mcp-server` 文件夹中实现一个新的 MCP 服务器，用于管理远程服务器的任务数据与本地 IDE 之间的同步。

## 核心功能

### 1. 数据同步
- **远程到本地**：从远程服务器拉取 Thread 数据（包含 state、tasks 等）
- **本地到远程**：将本地任务状态更新同步到远程数据库
- **本地存储**：数据存储在 `plans/thread-xxx/tasks.json` 格式

### 2. 任务管理
- 获取任务列表
- 创建新任务
- 更新任务状态
- 完成任务
- 删除任务
- 获取任务进度

## 技术架构

### 目录结构
```
planner-mcp-server/
├── package.json
├── tsconfig.json
├── src/
│   ├── index.ts                 # MCP 服务器入口
│   ├── server.ts               # 主服务器类
│   ├── types/                  # TypeScript 类型定义
│   │   ├── thread.ts
│   │   ├── task.ts
│   │   └── api.ts
│   ├── services/               # 业务逻辑层
│   │   ├── remote-api.ts       # 远程 API 调用
│   │   ├── local-storage.ts    # 本地文件存储
│   │   └── sync-manager.ts     # 同步管理器
│   ├── tools/                  # MCP 工具定义
│   │   ├── index.ts
│   │   ├── sync-tasks.ts       # 同步任务工具
│   │   ├── get-tasks.ts        # 获取任务工具
│   │   ├── update-task.ts      # 更新任务工具
│   │   ├── create-task.ts      # 创建任务工具
│   │   └── delete-task.ts      # 删除任务工具
│   └── utils/                  # 工具函数
│       ├── file-manager.ts     # 文件管理
│       ├── logger.ts           # 日志记录
│       └── config.ts           # 配置管理
└── plans/                      # 本地数据存储目录
    └── thread-xxx/
        ├── tasks.json          # 任务数据
        ├── state.json          # 状态数据
        └── metadata.json       # 元数据
```

### 数据模型

#### Thread 类型
```typescript
interface Thread {
  id: string;
  state: {
    originalPRD: string;
    structuredPRD: any;
    images: string[];
    [key: string]: any;
  };
  tasks: Task[];
  createdAt: string;
  updatedAt: string;
}
```

#### Task 类型
```typescript
interface Task {
  id: string;
  threadId: string;
  title: string;
  description: string;
  details: string;
  prompt: string;
  testStrategy: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  dependencies: string[];
  subtasks: SubTask[];
  createdAt: string;
  updatedAt: string;
}
```

## 核心服务设计

### 1. RemoteApiService
负责与远程服务器的 API 交互：
- 获取任务列表 (`/plan/getTasks`)
- 创建任务 (`/plan/createTask`)
- 更新任务状态 (`/plan/updateTaskStatus`)
- 完成任务 (`/plan/completeTask`)
- 删除任务 (`/plan/deleteTaskNew`)
- 获取任务进度 (`/plan/getTaskProgress`)
- 生成任务 (`/plan/generateTasks`)

### 2. LocalStorageService
管理本地文件存储：
- 读写 `plans/thread-xxx/tasks.json`
- 管理本地缓存
- 处理文件锁定和并发访问

### 3. SyncManager
协调本地和远程数据同步：
- 双向同步策略
- 冲突解决机制
- 增量同步优化
- 离线模式支持

## MCP 工具实现

### 1. sync-tasks
```typescript
{
  name: "sync_tasks",
  description: "同步远程任务到本地",
  inputSchema: {
    type: "object",
    properties: {
      threadId: { type: "string" },
      force: { type: "boolean", default: false }
    }
  }
}
```

### 2. get-tasks
```typescript
{
  name: "get_tasks",
  description: "获取本地任务列表",
  inputSchema: {
    type: "object",
    properties: {
      threadId: { type: "string" },
      status: { type: "string", enum: ["pending", "in_progress", "completed", "failed"] },
      priority: { type: "string", enum: ["high", "medium", "low"] }
    }
  }
}
```

### 3. update-task
```typescript
{
  name: "update_task",
  description: "更新任务状态并同步到远程",
  inputSchema: {
    type: "object",
    properties: {
      taskId: { type: "string" },
      status: { type: "string" },
      progress: { type: "number" },
      notes: { type: "string" }
    }
  }
}
```

## 配置管理

### 环境变量
```bash
# 远程服务器配置
REMOTE_API_BASE_URL=https://your-api-server.com
REMOTE_API_KEY=your-api-key

# 本地存储配置
LOCAL_STORAGE_PATH=./plans
SYNC_INTERVAL=300000  # 5分钟

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/planner-mcp.log
```

### 配置文件 (config.json)
```json
{
  "remote": {
    "baseUrl": "https://your-api-server.com",
    "timeout": 30000,
    "retryAttempts": 3
  },
  "local": {
    "storagePath": "./plans",
    "backupEnabled": true,
    "maxBackups": 10
  },
  "sync": {
    "autoSync": true,
    "interval": 300000,
    "conflictResolution": "remote_wins"
  }
}
```

## 实现计划

### 阶段一：基础架构 (1-2天)
1. 创建项目结构和配置文件
2. 实现基础类型定义
3. 搭建 MCP 服务器框架
4. 实现基础的日志和配置管理

### 阶段二：核心服务 (2-3天)
1. 实现 RemoteApiService
2. 实现 LocalStorageService
3. 实现基础的 SyncManager
4. 添加错误处理和重试机制

### 阶段三：MCP 工具 (2-3天)
1. 实现所有 MCP 工具
2. 添加输入验证和错误处理
3. 实现工具之间的协调逻辑
4. 添加详细的工具文档

### 阶段四：测试和优化 (1-2天)
1. 编写单元测试
2. 集成测试
3. 性能优化
4. 文档完善

## 使用示例

### 在 IDE 中的使用方式
```
# 同步远程任务
请帮我同步 thread-12345 的任务到本地

# 查看任务列表
显示当前项目的所有待处理任务

# 更新任务状态
将任务 task-001 的状态更新为进行中

# 创建新任务
基于当前需求创建一个新的开发任务
```

### MCP 配置
```json
{
  "mcpServers": {
    "planner": {
      "command": "node",
      "args": ["planner-mcp-server/dist/index.js"],
      "env": {
        "REMOTE_API_BASE_URL": "https://your-api-server.com",
        "REMOTE_API_KEY": "your-api-key"
      }
    }
  }
}
```

## 扩展性考虑

1. **插件系统**：支持自定义同步策略和数据处理器
2. **多项目支持**：管理多个项目的任务数据
3. **团队协作**：支持多用户环境下的数据同步
4. **集成扩展**：与其他开发工具的集成接口

这个方案提供了一个完整的任务管理和同步解决方案，既保持了与现有 claude-task-master 项目的兼容性，又提供了强大的远程数据同步能力。
