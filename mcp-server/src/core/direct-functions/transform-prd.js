/**
 * transform-prd.js
 * Direct function implementation for transforming unstructured PRD documents into structured format
 */

import path from 'path';
import { transformPRD } from '../../../../scripts/modules/task-manager.js';
import { createLogWrapper } from '../../tools/utils.js';

/**
 * Direct function wrapper for transforming unstructured PRD documents into structured format.
 *
 * @param {Object} args - Command arguments containing projectRoot, input, output options.
 * @param {Object} log - Logger object.
 * @param {Object} context - Context object containing session data.
 * @returns {Promise<Object>} - Result object with success status and data/error information.
 */
export async function transformPRDDirect(args, log, context = {}) {
	const { session } = context;
	// Extract arguments
	const {
		input: inputArg,
		output: outputArg,
		projectRoot
	} = args;

	// Create the standard logger wrapper
	const logWrapper = createLogWrapper(log);

	// --- Input Validation and Path Resolution ---
	if (!projectRoot) {
		logWrapper.error('transformPRDDirect requires a projectRoot argument.');
		return {
			success: false,
			error: {
				code: 'MISSING_ARGUMENT',
				message: 'projectRoot is required.'
			}
		};
	}
	if (!inputArg) {
		logWrapper.error('transformPRDDirect called without input path');
		return {
			success: false,
			error: { code: 'MISSING_ARGUMENT', message: 'Input path is required' }
		};
	}

	// Resolve input and output paths relative to projectRoot
	const inputPath = path.resolve(projectRoot, inputArg);
	const outputPath = outputArg
		? path.resolve(projectRoot, outputArg)
		: path.resolve(projectRoot, 'scripts', 'formatted-prd.txt'); // Default output path

	logWrapper.info(
		`Transforming PRD via direct function. Input: ${inputPath}, Output: ${outputPath}, ProjectRoot: ${projectRoot}`
	);

	try {
		// Call the core transformPRD function
		const result = await transformPRD(
			inputPath,
			outputPath,
			{
				session,
				mcpLog: logWrapper,
				projectRoot,
				commandName: 'transform-prd',
				outputType: 'mcp'
			}
		);

		// Check for success
		if (result && result.success) {
			const successMsg = `成功转换PRD并保存到 ${result.outputPath}`;
			logWrapper.success(successMsg);
			return {
				success: true,
				data: {
					message: successMsg,
					outputPath: result.outputPath,
					missingInfo: result.missingInfo || [],
					telemetryData: result.telemetryData
				}
			};
		} else {
			// Handle case where core function didn't return expected success structure
			logWrapper.error(
				'Core transformPRD function did not return a successful structure.'
			);
			return {
				success: false,
				error: {
					code: 'CORE_FUNCTION_ERROR',
					message:
						result?.message ||
						'Core function failed to transform PRD or returned unexpected result.'
				}
			};
		}
	} catch (error) {
		logWrapper.error(`Error executing core transformPRD: ${error.message}`);
		return {
			success: false,
			error: {
				code: 'TRANSFORM_PRD_CORE_ERROR',
				message: error.message || 'Unknown error transforming PRD'
			}
		};
	}
}
