/**
 * tools/transform-prd.js
 * Tool to transform unstructured PRD document into a structured format
 */

import { z } from 'zod';
import path from 'path';
import {
	handleApiResult,
	createErrorResponse,
	withNormalizedProjectRoot
} from './utils.js';
import { transformPRDDirect } from '../core/direct-functions/transform-prd.js';

/**
 * Register the transform_prd tool
 * @param {Object} server - FastMCP server instance
 */
export function registerTransformPRDTool(server) {
	server.addTool({
		name: 'transform_prd',
		description:
			"将不规范的PRD转换为格式化的PRD。把PRD中的页面原型图片转成格式化的XML描述，并且通过提问的形式提示用户补充缺少的信息。建议在执行parse-prd之前运行此工具，以确保PRD格式规范。",
		parameters: z.object({
			input: z
				.string()
				.optional()
				.default('scripts/prd.txt')
				.describe('不规范PRD文档文件的绝对路径 (.txt, .md等格式)'),
			output: z
				.string()
				.optional()
				.describe(
					'格式化后的PRD输出路径 (默认: scripts/formatted-prd.txt)'
				),
			projectRoot: z
				.string()
				.describe('项目的根目录。必须是绝对路径。')
		}),
		execute: withNormalizedProjectRoot(async (args, { log, session }) => {
			const toolName = 'transform_prd';
			try {
				log.info(
					`执行 ${toolName} 工具，参数: ${JSON.stringify(args)}`
				);

				// 调用直接函数 - 传递相关参数，包括projectRoot
				const result = await transformPRDDirect(
					{
						input: args.input,
						output: args.output,
						projectRoot: args.projectRoot
					},
					log,
					{ session }
				);

				log.info(
					`${toolName}: 直接函数执行结果: success=${result.success}`
				);

				// 如果转换成功并且有缺失信息的问题，将它们添加到响应中
				if (result.success && result.data && result.data.missingInfo && result.data.missingInfo.length > 0) {
					result.data.message += '\n\n需要补充的信息：\n' + result.data.missingInfo.map((q, i) => `${i+1}. ${q}`).join('\n');
				}

				return handleApiResult(result, log, '转换PRD时出错');
			} catch (error) {
				log.error(
					`${toolName} 工具执行时发生严重错误: ${error.message}`
				);
				return createErrorResponse(
					`内部工具错误 (${toolName}): ${error.message}`
				);
			}
		})
	});
}
