import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { SyncManager } from '../services/sync-manager.js';

/**
 * Input schema for sync tasks tool
 */
export const SyncTasksInputSchema = z.object({
  threadId: z.string().describe('Thread ID to sync tasks for'),
  force: z.boolean().optional().default(false).describe('Force sync even if local data is newer'),
  direction: z.enum(['pull', 'push', 'bidirectional']).optional().default('bidirectional')
    .describe('Sync direction: pull (remote->local), push (local->remote), or bidirectional'),
});

export type SyncTasksInput = z.infer<typeof SyncTasksInputSchema>;

/**
 * Sync tasks tool implementation
 */
export async function syncTasks(input: SyncTasksInput) {
  logger.info('Executing sync-tasks tool', { input });

  try {
    // Validate input
    const validatedInput = SyncTasksInputSchema.parse(input);
    const { threadId, force, direction } = validatedInput;

    const syncManager = SyncManager.getInstance();
    let result;

    switch (direction) {
      case 'pull':
        result = await syncManager.syncFromRemote(threadId, force);
        break;
      case 'push':
        result = await syncManager.syncToRemote(threadId);
        break;
      case 'bidirectional':
      default:
        result = await syncManager.syncThread(threadId, force);
        break;
    }

    if (result.success) {
      return `✅ 同步成功！\n\n` +
             `线程ID: ${threadId}\n` +
             `同步方向: ${direction}\n` +
             `更新任务数: ${result.tasksUpdated}\n` +
             `消息: ${result.message}\n\n` +
             (result.errors.length > 0 ? `⚠️ 警告:\n${result.errors.join('\n')}` : '');
    } else {
      throw new Error(`❌ 同步失败！\n\n` +
                     `线程ID: ${threadId}\n` +
                     `错误信息: ${result.message}\n\n` +
                     (result.errors.length > 0 ? `详细错误:\n${result.errors.join('\n')}` : ''));
    }
  } catch (error: any) {
    logger.error('sync-tasks tool failed', { error, input });
    throw new Error(`❌ 同步工具执行失败！\n\n错误信息: ${error.message}`);
  }
}

/**
 * Tool definition for MCP server
 */
export const syncTasksTool = {
  name: 'sync_tasks',
  description: '同步远程任务到本地，支持双向同步、强制同步等选项',
  inputSchema: SyncTasksInputSchema,
  handler: syncTasks,
};
