import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { SyncManager } from '../services/sync-manager.js';
import { LocalStorageService } from '../services/local-storage.js';
import { TaskPrioritySchema, CreateTaskSchema } from '../types/index.js';

/**
 * Input schema for create task tool
 */
export const CreateTaskInputSchema = CreateTaskSchema.extend({
  syncToRemote: z.boolean().optional().default(true).describe('Whether to sync the new task to remote server'),
});

export type CreateTaskInput = z.infer<typeof CreateTaskInputSchema>;

/**
 * Create task tool implementation
 */
export async function createTask(input: CreateTaskInput) {
  logger.info('Executing create-task tool', { input });

  try {
    // Validate input
    const validatedInput = CreateTaskInputSchema.parse(input);
    const { 
      threadId, 
      title, 
      description, 
      details, 
      prompt, 
      testStrategy, 
      priority, 
      dependencies,
      syncToRemote 
    } = validatedInput;

    const localStorage = LocalStorageService.getInstance();
    const syncManager = SyncManager.getInstance();

    // Ensure thread directory exists
    await localStorage.ensureThreadDirectory(threadId);

    // Prepare task data
    const taskData = {
      threadId,
      title,
      description,
      details: details || '',
      prompt: prompt || '',
      testStrategy: testStrategy || '',
      priority: priority || 'medium',
      dependencies: dependencies || [],
    };

    let newTask;

    if (syncToRemote) {
      // Create task via sync manager (creates on remote and syncs to local)
      newTask = await syncManager.createTask(threadId, taskData);
    } else {
      // Create task locally only
      newTask = await localStorage.addTask(threadId, taskData);
    }

    // Format the response
    const priorityEmoji = {
      high: '🔴',
      medium: '🟡',
      low: '🟢',
    };

    let responseText = `✅ 任务创建成功！\n\n`;
    responseText += `**任务信息:**\n`;
    responseText += `- ID: ${newTask.id}\n`;
    responseText += `- 标题: ${newTask.title}\n`;
    responseText += `- 描述: ${newTask.description}\n`;
    responseText += `- 优先级: ${priorityEmoji[newTask.priority]} ${newTask.priority}\n`;
    responseText += `- 状态: ⏳ ${newTask.status}\n`;
    responseText += `- 线程: ${threadId}\n`;

    if (newTask.details) {
      responseText += `- 详细信息: ${newTask.details.length > 100 ? newTask.details.substring(0, 100) + '...' : newTask.details}\n`;
    }

    if (newTask.prompt) {
      responseText += `- 提示: ${newTask.prompt.length > 100 ? newTask.prompt.substring(0, 100) + '...' : newTask.prompt}\n`;
    }

    if (newTask.testStrategy) {
      responseText += `- 测试策略: ${newTask.testStrategy.length > 100 ? newTask.testStrategy.substring(0, 100) + '...' : newTask.testStrategy}\n`;
    }

    if (newTask.dependencies.length > 0) {
      responseText += `- 依赖任务: ${newTask.dependencies.join(', ')}\n`;
    }

    responseText += `\n**创建时间:** ${new Date(newTask.createdAt).toLocaleString('zh-CN')}\n`;
    responseText += `**同步状态:** ${syncToRemote ? '已同步到远程服务器' : '仅本地创建'}\n\n`;

    // Add helpful next steps
    responseText += `**下一步操作建议:**\n`;
    responseText += `- 使用 \`update_task\` 更新任务状态\n`;
    responseText += `- 使用 \`get_tasks\` 查看所有任务\n`;
    if (!syncToRemote) {
      responseText += `- 使用 \`sync_tasks\` 同步到远程服务器\n`;
    }

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };

  } catch (error: any) {
    logger.error('create-task tool failed', { error, input });
    
    let errorMessage = `❌ 创建任务失败！\n\n错误信息: ${error.message}`;
    
    // Provide helpful error context
    if (error.message.includes('threadId')) {
      errorMessage += `\n\n💡 提示: 请确保线程ID正确，或先使用 \`sync_tasks\` 同步线程数据。`;
    } else if (error.message.includes('title')) {
      errorMessage += `\n\n💡 提示: 任务标题是必需的，请提供有意义的标题。`;
    } else if (error.message.includes('description')) {
      errorMessage += `\n\n💡 提示: 任务描述是必需的，请提供清晰的描述。`;
    }
    
    return {
      content: [
        {
          type: 'text',
          text: errorMessage,
        },
      ],
      isError: true,
    };
  }
}

/**
 * Tool definition for MCP server
 */
export const createTaskTool = {
  name: 'create_task',
  description: '创建新任务，支持设置标题、描述、优先级、依赖等，可选择是否同步到远程服务器',
  inputSchema: CreateTaskInputSchema,
  handler: createTask,
};
