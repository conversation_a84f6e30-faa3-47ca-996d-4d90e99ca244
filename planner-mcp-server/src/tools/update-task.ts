import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { SyncManager } from '../services/sync-manager.js';
import { LocalStorageService } from '../services/local-storage.js';
import { TaskStatusSchema, TaskPrioritySchema } from '../types/index.js';

/**
 * Input schema for update task tool
 */
export const UpdateTaskInputSchema = z.object({
  threadId: z.string().describe('Thread ID containing the task'),
  taskId: z.string().describe('Task ID to update'),
  status: TaskStatusSchema.optional().describe('New task status'),
  priority: TaskPrioritySchema.optional().describe('New task priority'),
  title: z.string().optional().describe('New task title'),
  description: z.string().optional().describe('New task description'),
  details: z.string().optional().describe('New task details'),
  prompt: z.string().optional().describe('New task prompt'),
  testStrategy: z.string().optional().describe('New task test strategy'),
  dependencies: z.array(z.string()).optional().describe('New task dependencies'),
  notes: z.string().optional().describe('Update notes (for status changes)'),
  syncToRemote: z.boolean().optional().default(true).describe('Whether to sync changes to remote server'),
});

export type UpdateTaskInput = z.infer<typeof UpdateTaskInputSchema>;

/**
 * Update task tool implementation
 */
export async function updateTask(input: UpdateTaskInput) {
  logger.info('Executing update-task tool', { input });

  try {
    // Validate input
    const validatedInput = UpdateTaskInputSchema.parse(input);
    const { 
      threadId, 
      taskId, 
      status, 
      priority, 
      title, 
      description, 
      details, 
      prompt, 
      testStrategy, 
      dependencies, 
      notes,
      syncToRemote 
    } = validatedInput;

    const localStorage = LocalStorageService.getInstance();
    const syncManager = SyncManager.getInstance();

    // Check if thread exists
    const threadExists = await localStorage.threadExists(threadId);
    if (!threadExists) {
      throw new Error(`❌ 线程不存在: ${threadId}\n\n请先同步该线程的任务数据。`);
    }

    // Check if task exists
    const existingTask = await localStorage.getTask(threadId, taskId);
    if (!existingTask) {
      throw new Error(`❌ 任务不存在: ${taskId}\n\n请检查任务ID是否正确。`);
    }

    // Prepare update data
    const updateData: any = {};
    if (status !== undefined) updateData.status = status;
    if (priority !== undefined) updateData.priority = priority;
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (details !== undefined) updateData.details = details;
    if (prompt !== undefined) updateData.prompt = prompt;
    if (testStrategy !== undefined) updateData.testStrategy = testStrategy;
    if (dependencies !== undefined) updateData.dependencies = dependencies;

    if (Object.keys(updateData).length === 0) {
      throw new Error(`⚠️ 没有提供任何更新数据\n\n请至少提供一个要更新的字段。`);
    }

    let updatedTask;

    if (syncToRemote && status !== undefined) {
      // If updating status and sync is enabled, use sync manager for remote sync
      updatedTask = await syncManager.updateTaskStatus(threadId, taskId, status, notes);
      
      // Update other fields locally if provided
      if (Object.keys(updateData).length > 1) {
        const otherUpdates = { ...updateData };
        delete otherUpdates.status;
        await localStorage.updateTask(threadId, taskId, otherUpdates);
      }
    } else {
      // Update locally only
      updatedTask = await localStorage.updateTask(threadId, taskId, updateData);
      
      if (!updatedTask) {
        throw new Error(`❌ 更新任务失败: ${taskId}`);
      }
    }

    // Format the response
    const changes: string[] = [];
    if (status !== undefined && status !== existingTask.status) {
      changes.push(`状态: ${existingTask.status} → ${status}`);
    }
    if (priority !== undefined && priority !== existingTask.priority) {
      changes.push(`优先级: ${existingTask.priority} → ${priority}`);
    }
    if (title !== undefined && title !== existingTask.title) {
      changes.push(`标题: ${existingTask.title} → ${title}`);
    }
    if (description !== undefined && description !== existingTask.description) {
      changes.push(`描述: 已更新`);
    }
    if (details !== undefined && details !== existingTask.details) {
      changes.push(`详细信息: 已更新`);
    }
    if (prompt !== undefined && prompt !== existingTask.prompt) {
      changes.push(`提示: 已更新`);
    }
    if (testStrategy !== undefined && testStrategy !== existingTask.testStrategy) {
      changes.push(`测试策略: 已更新`);
    }
    if (dependencies !== undefined) {
      changes.push(`依赖: 已更新`);
    }

    let responseText = `✅ 任务更新成功！\n\n`;
    responseText += `**任务信息:**\n`;
    responseText += `- ID: ${taskId}\n`;
    responseText += `- 标题: ${updatedTask?.title || existingTask.title}\n`;
    responseText += `- 线程: ${threadId}\n\n`;
    
    if (changes.length > 0) {
      responseText += `**更新内容:**\n`;
      changes.forEach(change => {
        responseText += `- ${change}\n`;
      });
      responseText += '\n';
    }

    if (notes) {
      responseText += `**更新说明:** ${notes}\n\n`;
    }

    responseText += `**同步状态:** ${syncToRemote ? '已同步到远程服务器' : '仅本地更新'}\n`;
    responseText += `**更新时间:** ${new Date().toLocaleString('zh-CN')}`;

    return responseText;

  } catch (error: any) {
    logger.error('update-task tool failed', { error, input });
    throw new Error(`❌ 更新任务失败！\n\n错误信息: ${error.message}`);
  }
}

/**
 * Tool definition for MCP server
 */
export const updateTaskTool = {
  name: 'update_task',
  description: '更新任务信息，包括状态、优先级、标题等，支持同步到远程服务器',
  inputSchema: UpdateTaskInputSchema,
  handler: updateTask,
};
