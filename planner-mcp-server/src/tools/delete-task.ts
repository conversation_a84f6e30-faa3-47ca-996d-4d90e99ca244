import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { SyncManager } from '../services/sync-manager.js';
import { LocalStorageService } from '../services/local-storage.js';

/**
 * Input schema for delete task tool
 */
export const DeleteTaskInputSchema = z.object({
  threadId: z.string().describe('Thread ID containing the task'),
  taskId: z.string().describe('Task ID to delete'),
  syncToRemote: z.boolean().optional().default(true).describe('Whether to sync deletion to remote server'),
  force: z.boolean().optional().default(false).describe('Force delete even if task has dependencies'),
});

export type DeleteTaskInput = z.infer<typeof DeleteTaskInputSchema>;

/**
 * Delete task tool implementation
 */
export async function deleteTask(input: DeleteTaskInput) {
  logger.info('Executing delete-task tool', { input });

  try {
    // Validate input
    const validatedInput = DeleteTaskInputSchema.parse(input);
    const { threadId, taskId, syncToRemote, force } = validatedInput;

    const localStorage = LocalStorageService.getInstance();
    const syncManager = SyncManager.getInstance();

    // Check if thread exists
    const threadExists = await localStorage.threadExists(threadId);
    if (!threadExists) {
      throw new Error(`❌ 线程不存在: ${threadId}\n\n请先同步该线程的任务数据。`);
    }

    // Check if task exists and get its details
    const existingTask = await localStorage.getTask(threadId, taskId);
    if (!existingTask) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ 任务不存在: ${taskId}\n\n请检查任务ID是否正确。`,
          },
        ],
        isError: true,
      };
    }

    // Check for dependent tasks (tasks that depend on this task)
    if (!force) {
      const allTasks = await localStorage.getTasks(threadId);
      const dependentTasks = allTasks.filter(task => 
        task.dependencies.includes(taskId) && task.id !== taskId
      );

      if (dependentTasks.length > 0) {
        const dependentTaskTitles = dependentTasks.map(task => `- ${task.title} (${task.id})`).join('\n');
        
        return {
          content: [
            {
              type: 'text',
              text: `⚠️ 无法删除任务，存在依赖关系！\n\n` +
                    `任务 "${existingTask.title}" 被以下任务依赖:\n\n${dependentTaskTitles}\n\n` +
                    `请先处理这些依赖关系，或使用 \`force: true\` 强制删除。`,
            },
          ],
          isError: true,
        };
      }
    }

    let success = false;

    if (syncToRemote) {
      // Delete via sync manager (deletes from remote and local)
      success = await syncManager.deleteTask(threadId, taskId);
    } else {
      // Delete locally only
      success = await localStorage.deleteTask(threadId, taskId);
    }

    if (!success) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ 删除任务失败: ${taskId}\n\n请检查任务是否存在或网络连接是否正常。`,
          },
        ],
        isError: true,
      };
    }

    // If force delete, also clean up dependencies in other tasks
    if (force) {
      const allTasks = await localStorage.getTasks(threadId);
      let updatedCount = 0;

      for (const task of allTasks) {
        if (task.dependencies.includes(taskId)) {
          const updatedDependencies = task.dependencies.filter(dep => dep !== taskId);
          await localStorage.updateTask(threadId, task.id, {
            dependencies: updatedDependencies,
          });
          updatedCount++;
        }
      }

      if (updatedCount > 0) {
        logger.info(`Cleaned up dependencies in ${updatedCount} tasks after force delete`);
      }
    }

    // Format the response
    let responseText = `✅ 任务删除成功！\n\n`;
    responseText += `**已删除任务:**\n`;
    responseText += `- ID: ${taskId}\n`;
    responseText += `- 标题: ${existingTask.title}\n`;
    responseText += `- 描述: ${existingTask.description}\n`;
    responseText += `- 状态: ${existingTask.status}\n`;
    responseText += `- 优先级: ${existingTask.priority}\n`;
    responseText += `- 线程: ${threadId}\n\n`;

    if (existingTask.dependencies.length > 0) {
      responseText += `**原依赖任务:** ${existingTask.dependencies.join(', ')}\n\n`;
    }

    if (force) {
      responseText += `**强制删除:** 是 (已清理相关依赖关系)\n`;
    }

    responseText += `**同步状态:** ${syncToRemote ? '已从远程服务器删除' : '仅本地删除'}\n`;
    responseText += `**删除时间:** ${new Date().toLocaleString('zh-CN')}\n\n`;

    // Add helpful next steps
    responseText += `**建议操作:**\n`;
    responseText += `- 使用 \`get_tasks\` 查看剩余任务\n`;
    if (!syncToRemote) {
      responseText += `- 使用 \`sync_tasks\` 同步删除到远程服务器\n`;
    }

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };

  } catch (error: any) {
    logger.error('delete-task tool failed', { error, input });
    
    let errorMessage = `❌ 删除任务失败！\n\n错误信息: ${error.message}`;
    
    // Provide helpful error context
    if (error.message.includes('not found')) {
      errorMessage += `\n\n💡 提示: 任务可能已被删除或不存在，请使用 \`get_tasks\` 查看当前任务列表。`;
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      errorMessage += `\n\n💡 提示: 网络连接问题，可以尝试仅本地删除 (\`syncToRemote: false\`)，稍后再同步。`;
    }
    
    return {
      content: [
        {
          type: 'text',
          text: errorMessage,
        },
      ],
      isError: true,
    };
  }
}

/**
 * Tool definition for MCP server
 */
export const deleteTaskTool = {
  name: 'delete_task',
  description: '删除任务，支持检查依赖关系和强制删除，可选择是否同步到远程服务器',
  inputSchema: DeleteTaskInputSchema,
  handler: deleteTask,
};
