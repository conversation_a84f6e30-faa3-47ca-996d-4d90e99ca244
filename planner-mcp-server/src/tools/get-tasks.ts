import { z } from 'zod';
import { logger } from '../utils/logger.js';
import { LocalStorageService } from '../services/local-storage.js';
import { TaskFilterSchema, Task } from '../types/index.js';

/**
 * Input schema for get tasks tool
 */
export const GetTasksInputSchema = z.object({
  threadId: z.string().describe('Thread ID to get tasks for'),
  status: z.enum(['pending', 'in_progress', 'completed', 'failed']).optional()
    .describe('Filter tasks by status'),
  priority: z.enum(['high', 'medium', 'low']).optional()
    .describe('Filter tasks by priority'),
  search: z.string().optional()
    .describe('Search tasks by title, description, or details'),
  includeDetails: z.boolean().optional().default(false)
    .describe('Include detailed information for each task'),
});

export type GetTasksInput = z.infer<typeof GetTasksInputSchema>;

/**
 * Format task for display
 */
function formatTask(task: Task, includeDetails: boolean): string {
  const statusEmoji = {
    pending: '⏳',
    in_progress: '🔄',
    completed: '✅',
    failed: '❌',
  };

  const priorityEmoji = {
    high: '🔴',
    medium: '🟡',
    low: '🟢',
  };

  let formatted = `${statusEmoji[task.status]} ${priorityEmoji[task.priority]} **${task.title}**\n`;
  formatted += `   ID: ${task.id}\n`;
  formatted += `   状态: ${task.status}\n`;
  formatted += `   优先级: ${task.priority}\n`;
  formatted += `   描述: ${task.description}\n`;

  if (task.dependencies.length > 0) {
    formatted += `   依赖: ${task.dependencies.join(', ')}\n`;
  }

  if (task.subtasks.length > 0) {
    formatted += `   子任务: ${task.subtasks.length} 个\n`;
  }

  if (includeDetails) {
    if (task.details) {
      formatted += `   详细信息: ${task.details}\n`;
    }
    if (task.prompt) {
      formatted += `   提示: ${task.prompt}\n`;
    }
    if (task.testStrategy) {
      formatted += `   测试策略: ${task.testStrategy}\n`;
    }
  }

  formatted += `   创建时间: ${new Date(task.createdAt).toLocaleString('zh-CN')}\n`;
  formatted += `   更新时间: ${new Date(task.updatedAt).toLocaleString('zh-CN')}\n`;

  return formatted;
}

/**
 * Get tasks tool implementation
 */
export async function getTasks(input: GetTasksInput) {
  logger.info('Executing get-tasks tool', { input });

  try {
    // Validate input
    const validatedInput = GetTasksInputSchema.parse(input);
    const { threadId, status, priority, search, includeDetails } = validatedInput;

    const localStorage = LocalStorageService.getInstance();

    // Check if thread exists
    const threadExists = await localStorage.threadExists(threadId);
    if (!threadExists) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ 线程不存在: ${threadId}\n\n请先同步该线程的任务数据。`,
          },
        ],
        isError: true,
      };
    }

    // Get filtered tasks
    const filter = TaskFilterSchema.parse({
      threadId,
      status,
      priority,
      search,
    });

    const tasks = await localStorage.filterTasks(threadId, filter);

    if (tasks.length === 0) {
      let message = `📋 线程 ${threadId} 中没有找到任务`;
      if (status || priority || search) {
        message += '\n\n筛选条件:';
        if (status) message += `\n- 状态: ${status}`;
        if (priority) message += `\n- 优先级: ${priority}`;
        if (search) message += `\n- 搜索: ${search}`;
      }
      
      return {
        content: [
          {
            type: 'text',
            text: message,
          },
        ],
      };
    }

    // Sort tasks by priority and status
    const sortedTasks = tasks.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const statusOrder = { pending: 4, in_progress: 3, failed: 2, completed: 1 };
      
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      return statusOrder[b.status] - statusOrder[a.status];
    });

    // Format tasks for display
    let content = `📋 **任务列表** (线程: ${threadId})\n`;
    content += `找到 ${tasks.length} 个任务\n\n`;

    if (status || priority || search) {
      content += '**筛选条件:**\n';
      if (status) content += `- 状态: ${status}\n`;
      if (priority) content += `- 优先级: ${priority}\n`;
      if (search) content += `- 搜索: ${search}\n`;
      content += '\n';
    }

    // Group tasks by status for better organization
    const tasksByStatus = {
      pending: sortedTasks.filter(t => t.status === 'pending'),
      in_progress: sortedTasks.filter(t => t.status === 'in_progress'),
      completed: sortedTasks.filter(t => t.status === 'completed'),
      failed: sortedTasks.filter(t => t.status === 'failed'),
    };

    const statusLabels = {
      pending: '⏳ 待处理',
      in_progress: '🔄 进行中',
      completed: '✅ 已完成',
      failed: '❌ 失败',
    };

    for (const [status, statusTasks] of Object.entries(tasksByStatus)) {
      if (statusTasks.length > 0) {
        content += `## ${statusLabels[status as keyof typeof statusLabels]} (${statusTasks.length})\n\n`;
        for (const task of statusTasks) {
          content += formatTask(task, includeDetails) + '\n';
        }
      }
    }

    return {
      content: [
        {
          type: 'text',
          text: content,
        },
      ],
    };

  } catch (error: any) {
    logger.error('get-tasks tool failed', { error, input });
    
    return {
      content: [
        {
          type: 'text',
          text: `❌ 获取任务失败！\n\n错误信息: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
}

/**
 * Tool definition for MCP server
 */
export const getTasksTool = {
  name: 'get_tasks',
  description: '获取本地任务列表，支持按状态、优先级和关键词筛选',
  inputSchema: GetTasksInputSchema,
  handler: getTasks,
};
