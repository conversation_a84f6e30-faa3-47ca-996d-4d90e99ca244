import { FastMCP } from 'fastmcp';
import { logger } from '../utils/logger.js';

// Import all tools
import { syncTasksTool } from './sync-tasks.js';
import { getTasksTool } from './get-tasks.js';
import { updateTaskTool } from './update-task.js';
import { createTaskTool } from './create-task.js';
import { deleteTaskTool } from './delete-task.js';

/**
 * Register all planner MCP tools
 */
export function registerPlannerTools(server: FastMCP): void {
  logger.info('Registering planner MCP tools');

  // Register sync tasks tool
  server.addTool({
    name: syncTasksTool.name,
    description: syncTasksTool.description,
    parameters: syncTasksTool.inputSchema,
    execute: async (input: any) => syncTasksTool.handler(input),
  });

  // Register get tasks tool
  server.addTool({
    name: getTasksTool.name,
    description: getTasksTool.description,
    parameters: getTasksTool.inputSchema,
    execute: async (input: any) => getTasksTool.handler(input),
  });

  // Register update task tool
  server.addTool({
    name: updateTaskTool.name,
    description: updateTaskTool.description,
    parameters: updateTaskTool.inputSchema,
    execute: async (input: any) => updateTaskTool.handler(input),
  });

  // Register create task tool
  server.addTool({
    name: createTaskTool.name,
    description: createTaskTool.description,
    parameters: createTaskTool.inputSchema,
    execute: async (input: any) => createTaskTool.handler(input),
  });

  // Register delete task tool
  server.addTool({
    name: deleteTaskTool.name,
    description: deleteTaskTool.description,
    parameters: deleteTaskTool.inputSchema,
    execute: async (input: any) => deleteTaskTool.handler(input),
  });

  logger.info('All planner MCP tools registered successfully');
}

// Export individual tools for testing
export {
  syncTasksTool,
  getTasksTool,
  updateTaskTool,
  createTaskTool,
  deleteTaskTool,
};
