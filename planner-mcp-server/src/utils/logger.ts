import fs from 'fs-extra';
import path from 'path';
import { ConfigManager } from './config.js';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Simple logger implementation
 */
export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private logFile?: string | undefined;

  private constructor() {
    const config = ConfigManager.getInstance().getLoggingConfig();
    this.logLevel = config.level;
    this.logFile = config.file;
    
    // Ensure log directory exists
    if (this.logFile) {
      const logDir = path.dirname(this.logFile);
      fs.ensureDirSync(logDir);
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.logLevel);
  }

  private formatMessage(level: LogLevel, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
  }

  private writeLog(level: LogLevel, message: string, meta?: any): void {
    const formattedMessage = this.formatMessage(level, message, meta);
    
    // Console output
    if (this.shouldLog(level)) {
      switch (level) {
        case 'debug':
          console.debug(formattedMessage);
          break;
        case 'info':
          console.info(formattedMessage);
          break;
        case 'warn':
          console.warn(formattedMessage);
          break;
        case 'error':
          console.error(formattedMessage);
          break;
      }
    }

    // File output
    if (this.logFile && this.shouldLog(level)) {
      try {
        fs.appendFileSync(this.logFile, formattedMessage + '\n');
      } catch (error) {
        console.error('Failed to write to log file:', error);
      }
    }
  }

  public debug(message: string, meta?: any): void {
    this.writeLog('debug', message, meta);
  }

  public info(message: string, meta?: any): void {
    this.writeLog('info', message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.writeLog('warn', message, meta);
  }

  public error(message: string, meta?: any): void {
    this.writeLog('error', message, meta);
  }

  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  public setLogFile(file: string): void {
    this.logFile = file;
    const logDir = path.dirname(file);
    fs.ensureDirSync(logDir);
  }
}

// Export singleton instance
export const logger = Logger.getInstance();
