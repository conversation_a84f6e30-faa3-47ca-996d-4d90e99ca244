import { logger } from '../utils/logger.js';
import { ConfigManager } from '../utils/config.js';
import { RemoteApiService } from './remote-api.js';
import { LocalStorageService } from './local-storage.js';
import {
  Task,
  Thread,
  CreateTaskInput,
  UpdateTaskStatusInput,
  GetTasksDto,
} from '../types/index.js';

/**
 * Sync result interface
 */
interface SyncResult {
  success: boolean;
  message: string;
  tasksUpdated: number;
  errors: string[];
}

/**
 * Sync manager for coordinating data between remote API and local storage
 */
export class SyncManager {
  private static instance: SyncManager;
  private remoteApi: RemoteApiService;
  private localStorage: LocalStorageService;
  private config: ReturnType<ConfigManager['getSyncConfig']>;
  private syncInterval?: NodeJS.Timeout | undefined;

  private constructor() {
    this.remoteApi = RemoteApiService.getInstance();
    this.localStorage = LocalStorageService.getInstance();
    this.config = ConfigManager.getInstance().getSyncConfig();
    
    logger.info('SyncManager initialized');
    
    // Start auto-sync if enabled
    if (this.config.autoSync) {
      this.startAutoSync();
    }
  }

  public static getInstance(): SyncManager {
    if (!SyncManager.instance) {
      SyncManager.instance = new SyncManager();
    }
    return SyncManager.instance;
  }

  /**
   * Start automatic synchronization
   */
  public startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      try {
        const threads = await this.localStorage.listThreads();
        for (const threadId of threads) {
          await this.syncThread(threadId, false);
        }
      } catch (error) {
        logger.error('Auto-sync failed', { error });
      }
    }, this.config.interval);

    logger.info(`Auto-sync started with interval: ${this.config.interval}ms`);
  }

  /**
   * Stop automatic synchronization
   */
  public stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
      logger.info('Auto-sync stopped');
    }
  }

  /**
   * Sync tasks from remote to local
   */
  public async syncFromRemote(threadId: string, force = false): Promise<SyncResult> {
    logger.info(`Syncing tasks from remote for thread: ${threadId}`, { force });
    
    const result: SyncResult = {
      success: false,
      message: '',
      tasksUpdated: 0,
      errors: [],
    };

    try {
      // Get tasks from remote API
      const getTasksDto: GetTasksDto = {
        threadId,
        pageSize: 1000, // Get all tasks
        pageNo: 1,
      };

      const remoteTasks = await this.remoteApi.getTasks(getTasksDto);
      logger.debug(`Retrieved ${remoteTasks.length} tasks from remote`);

      // Get local tasks
      const localTasks = await this.localStorage.getTasks(threadId);
      logger.debug(`Found ${localTasks.length} local tasks`);

      // Determine which tasks need to be updated
      const tasksToUpdate: Task[] = [];
      
      for (const remoteTask of remoteTasks) {
        const localTask = localTasks.find(t => t.id === remoteTask.id);
        
        if (!localTask) {
          // New task from remote
          tasksToUpdate.push(remoteTask);
        } else if (force || new Date(remoteTask.updatedAt) > new Date(localTask.updatedAt)) {
          // Remote task is newer or force sync
          tasksToUpdate.push(remoteTask);
        }
      }

      // Save updated tasks to local storage
      if (tasksToUpdate.length > 0) {
        // Merge with existing local tasks
        const updatedLocalTasks = [...localTasks];
        
        for (const taskToUpdate of tasksToUpdate) {
          const existingIndex = updatedLocalTasks.findIndex(t => t.id === taskToUpdate.id);
          if (existingIndex >= 0) {
            updatedLocalTasks[existingIndex] = taskToUpdate;
          } else {
            updatedLocalTasks.push(taskToUpdate);
          }
        }

        await this.localStorage.saveTasks(threadId, updatedLocalTasks);
        result.tasksUpdated = tasksToUpdate.length;
      }

      result.success = true;
      result.message = `Successfully synced ${result.tasksUpdated} tasks from remote`;
      logger.info(result.message);

    } catch (error: any) {
      result.errors.push(error.message);
      result.message = `Failed to sync from remote: ${error.message}`;
      logger.error(result.message, { error });
    }

    return result;
  }

  /**
   * Sync tasks from local to remote
   */
  public async syncToRemote(threadId: string): Promise<SyncResult> {
    logger.info(`Syncing tasks to remote for thread: ${threadId}`);
    
    const result: SyncResult = {
      success: false,
      message: '',
      tasksUpdated: 0,
      errors: [],
    };

    try {
      // Get local tasks
      const localTasks = await this.localStorage.getTasks(threadId);
      
      // Get remote tasks for comparison
      const getTasksDto: GetTasksDto = {
        threadId,
        pageSize: 1000,
        pageNo: 1,
      };
      
      const remoteTasks = await this.remoteApi.getTasks(getTasksDto);
      
      // Find tasks that need to be synced to remote
      for (const localTask of localTasks) {
        try {
          const remoteTask = remoteTasks.find(t => t.id === localTask.id);
          
          if (!remoteTask) {
            // Task doesn't exist on remote, create it
            await this.remoteApi.createTask({
              threadId: localTask.threadId,
              title: localTask.title,
              description: localTask.description,
              details: localTask.details,
              prompt: localTask.prompt,
              testStrategy: localTask.testStrategy,
              priority: localTask.priority,
              dependencies: localTask.dependencies,
            });
            result.tasksUpdated++;
          } else if (new Date(localTask.updatedAt) > new Date(remoteTask.updatedAt)) {
            // Local task is newer, update remote
            await this.remoteApi.updateTaskStatus({
              taskId: localTask.id,
              status: localTask.status,
            });
            result.tasksUpdated++;
          }
        } catch (error: any) {
          result.errors.push(`Failed to sync task ${localTask.id}: ${error.message}`);
          logger.error(`Failed to sync task ${localTask.id} to remote`, { error });
        }
      }

      result.success = result.errors.length === 0;
      result.message = result.success 
        ? `Successfully synced ${result.tasksUpdated} tasks to remote`
        : `Synced ${result.tasksUpdated} tasks with ${result.errors.length} errors`;
      
      logger.info(result.message);

    } catch (error: any) {
      result.errors.push(error.message);
      result.message = `Failed to sync to remote: ${error.message}`;
      logger.error(result.message, { error });
    }

    return result;
  }

  /**
   * Bidirectional sync for a thread
   */
  public async syncThread(threadId: string, force = false): Promise<SyncResult> {
    logger.info(`Starting bidirectional sync for thread: ${threadId}`, { force });
    
    const result: SyncResult = {
      success: false,
      message: '',
      tasksUpdated: 0,
      errors: [],
    };

    try {
      // First sync from remote (pull)
      const pullResult = await this.syncFromRemote(threadId, force);
      result.tasksUpdated += pullResult.tasksUpdated;
      result.errors.push(...pullResult.errors);

      // Then sync to remote (push)
      const pushResult = await this.syncToRemote(threadId);
      result.tasksUpdated += pushResult.tasksUpdated;
      result.errors.push(...pushResult.errors);

      result.success = pullResult.success && pushResult.success;
      result.message = `Bidirectional sync completed. Updated ${result.tasksUpdated} tasks total`;
      
      if (result.errors.length > 0) {
        result.message += ` with ${result.errors.length} errors`;
      }

      logger.info(result.message);

    } catch (error: any) {
      result.errors.push(error.message);
      result.message = `Bidirectional sync failed: ${error.message}`;
      logger.error(result.message, { error });
    }

    return result;
  }

  /**
   * Create task and sync to remote
   */
  public async createTask(threadId: string, taskInput: CreateTaskInput): Promise<Task> {
    logger.info(`Creating task for thread: ${threadId}`, { taskInput });

    try {
      // Create task on remote first
      const remoteTask = await this.remoteApi.createTask(taskInput);
      
      // Save to local storage
      const localTasks = await this.localStorage.getTasks(threadId);
      localTasks.push(remoteTask);
      await this.localStorage.saveTasks(threadId, localTasks);
      
      logger.info(`Task created and synced: ${remoteTask.id}`);
      return remoteTask;

    } catch (error: any) {
      logger.error(`Failed to create task: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Update task status and sync to remote
   */
  public async updateTaskStatus(threadId: string, taskId: string, status: string, notes?: string): Promise<Task> {
    logger.info(`Updating task status: ${taskId} to ${status}`, { notes });

    try {
      // Update on remote first
      const updatedTask = await this.remoteApi.updateTaskStatus({
        taskId,
        status: status as any,
        notes,
      });

      // Update local storage
      await this.localStorage.updateTask(threadId, taskId, {
        status: status as any,
      });

      logger.info(`Task status updated and synced: ${taskId}`);
      return updatedTask;

    } catch (error: any) {
      logger.error(`Failed to update task status: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Delete task and sync to remote
   */
  public async deleteTask(threadId: string, taskId: string): Promise<boolean> {
    logger.info(`Deleting task: ${taskId} from thread: ${threadId}`);

    try {
      // Delete from remote first
      const success = await this.remoteApi.deleteTask({ taskId });
      
      if (success) {
        // Delete from local storage
        await this.localStorage.deleteTask(threadId, taskId);
        logger.info(`Task deleted and synced: ${taskId}`);
      }

      return success;

    } catch (error: any) {
      logger.error(`Failed to delete task: ${error.message}`, { error });
      throw error;
    }
  }
}
