import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { logger } from '../utils/logger.js';
import { ConfigManager } from '../utils/config.js';
import {
  Task,
  ApiResponse,
  GetTasksDto,
  CreateTaskDto,
  UpdateTaskStatusDto,
  CompleteTaskDto,
  DeleteTaskNewDto,
  GenerateTasksDto,
  TaskProgress,
} from '../types/index.js';

/**
 * Remote API service for communicating with the backend
 */
export class RemoteApiService {
  private static instance: RemoteApiService;
  private client: AxiosInstance;
  private config: ReturnType<ConfigManager['getRemoteConfig']>;

  private constructor() {
    this.config = ConfigManager.getInstance().getRemoteConfig();
    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
      },
    });

    this.setupInterceptors();
    logger.info(`RemoteApiService initialized with base URL: ${this.config.baseUrl}`);
  }

  public static getInstance(): RemoteApiService {
    if (!RemoteApiService.instance) {
      RemoteApiService.instance = new RemoteApiService();
    }
    return RemoteApiService.instance;
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('API Request Error', { error });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status} ${response.config.url}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        logger.error('API Response Error', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make API request with retry logic
   */
  private async makeRequest<T>(
    requestConfig: AxiosRequestConfig,
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.request<ApiResponse<T>>(requestConfig);
      return response.data;
    } catch (error: any) {
      if (retryCount < this.config.retryAttempts) {
        logger.warn(`API request failed, retrying... (${retryCount + 1}/${this.config.retryAttempts})`);
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        return this.makeRequest<T>(requestConfig, retryCount + 1);
      }
      
      logger.error('API request failed after all retries', { error });
      throw new Error(`API request failed: ${error.message}`);
    }
  }

  /**
   * Get tasks from remote API
   */
  public async getTasks(params: GetTasksDto): Promise<Task[]> {
    logger.info('Fetching tasks from remote API', { params });
    
    const response = await this.makeRequest<Task[]>({
      method: 'GET',
      url: '/plan/getTasks',
      params,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to get tasks');
    }

    return response.data || [];
  }

  /**
   * Create task via remote API
   */
  public async createTask(data: CreateTaskDto): Promise<Task> {
    logger.info('Creating task via remote API', { data });
    
    const response = await this.makeRequest<Task>({
      method: 'POST',
      url: '/plan/createTask',
      data,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to create task');
    }

    if (!response.data) {
      throw new Error('No task data returned from API');
    }

    return response.data;
  }

  /**
   * Update task status via remote API
   */
  public async updateTaskStatus(data: UpdateTaskStatusDto): Promise<Task> {
    logger.info('Updating task status via remote API', { data });
    
    const response = await this.makeRequest<Task>({
      method: 'POST',
      url: '/plan/updateTaskStatus',
      data,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to update task status');
    }

    if (!response.data) {
      throw new Error('No task data returned from API');
    }

    return response.data;
  }

  /**
   * Complete task via remote API
   */
  public async completeTask(data: CompleteTaskDto): Promise<Task> {
    logger.info('Completing task via remote API', { data });
    
    const response = await this.makeRequest<Task>({
      method: 'POST',
      url: '/plan/completeTask',
      data,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to complete task');
    }

    if (!response.data) {
      throw new Error('No task data returned from API');
    }

    return response.data;
  }

  /**
   * Delete task via remote API
   */
  public async deleteTask(data: DeleteTaskNewDto): Promise<boolean> {
    logger.info('Deleting task via remote API', { data });
    
    const response = await this.makeRequest<boolean>({
      method: 'POST',
      url: '/plan/deleteTaskNew',
      data,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete task');
    }

    return response.data || false;
  }

  /**
   * Get task progress via remote API
   */
  public async getTaskProgress(threadId: string): Promise<TaskProgress> {
    logger.info('Getting task progress from remote API', { threadId });
    
    const response = await this.makeRequest<TaskProgress>({
      method: 'GET',
      url: '/plan/getTaskProgress',
      params: { threadId },
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to get task progress');
    }

    if (!response.data) {
      throw new Error('No progress data returned from API');
    }

    return response.data;
  }

  /**
   * Generate tasks via remote API
   */
  public async generateTasks(data: GenerateTasksDto): Promise<Task[]> {
    logger.info('Generating tasks via remote API', { data });
    
    const response = await this.makeRequest<Task[]>({
      method: 'POST',
      url: '/plan/generateTasks',
      data,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to generate tasks');
    }

    return response.data || [];
  }
}
