import { z } from 'zod';

/**
 * Thread state schema
 */
export const ThreadStateSchema = z.object({
  originalPRD: z.string(),
  structuredPRD: z.any(),
  images: z.array(z.string()),
}).passthrough(); // Allow additional properties

/**
 * Thread schema
 */
export const ThreadSchema = z.object({
  id: z.string(),
  state: ThreadStateSchema,
  tasks: z.array(z.any()), // Will be refined with Task schema
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Thread type
 */
export type Thread = z.infer<typeof ThreadSchema>;

/**
 * Thread state type
 */
export type ThreadState = z.infer<typeof ThreadStateSchema>;

/**
 * Partial thread for updates
 */
export type PartialThread = Partial<Thread>;

/**
 * Thread creation input
 */
export const CreateThreadSchema = z.object({
  state: ThreadStateSchema,
  tasks: z.array(z.any()).optional().default([]),
});

export type CreateThreadInput = z.infer<typeof CreateThreadSchema>;
