import { z } from 'zod';
import { TaskSchema, CreateTaskSchema, UpdateTaskStatusSchema } from './task.js';

/**
 * API Response wrapper
 */
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

/**
 * Get Tasks DTO
 */
export const GetTasksDtoSchema = z.object({
  threadId: z.string(),
  pageSize: z.number().optional().default(20),
  pageNo: z.number().optional().default(1),
  sortBy: z.string().optional(),
  filter: z.record(z.any()).optional(),
});

/**
 * Create Task DTO
 */
export const CreateTaskDtoSchema = CreateTaskSchema;

/**
 * Update Task Status DTO
 */
export const UpdateTaskStatusDtoSchema = UpdateTaskStatusSchema;

/**
 * Complete Task DTO
 */
export const CompleteTaskDtoSchema = z.object({
  taskId: z.string(),
  completionNotes: z.string().optional(),
});

/**
 * Delete Task DTO
 */
export const DeleteTaskNewDtoSchema = z.object({
  taskId: z.string(),
});

/**
 * Generate Tasks DTO
 */
export const GenerateTasksDtoSchema = z.object({
  threadId: z.string(),
  prd: z.string(),
  context: z.string().optional(),
});

/**
 * Task Progress Response
 */
export const TaskProgressSchema = z.object({
  threadId: z.string(),
  totalTasks: z.number(),
  completedTasks: z.number(),
  inProgressTasks: z.number(),
  pendingTasks: z.number(),
  failedTasks: z.number(),
  progress: z.number(), // percentage
});

/**
 * API Configuration
 */
export const ApiConfigSchema = z.object({
  baseUrl: z.string(),
  apiKey: z.string().optional(),
  timeout: z.number().optional().default(30000),
  retryAttempts: z.number().optional().default(3),
  retryDelay: z.number().optional().default(1000),
});

/**
 * Exported types
 */
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T };
export type GetTasksDto = z.infer<typeof GetTasksDtoSchema>;
export type CreateTaskDto = z.infer<typeof CreateTaskDtoSchema>;
export type UpdateTaskStatusDto = z.infer<typeof UpdateTaskStatusDtoSchema>;
export type CompleteTaskDto = z.infer<typeof CompleteTaskDtoSchema>;
export type DeleteTaskNewDto = z.infer<typeof DeleteTaskNewDtoSchema>;
export type GenerateTasksDto = z.infer<typeof GenerateTasksDtoSchema>;
export type TaskProgress = z.infer<typeof TaskProgressSchema>;
export type ApiConfig = z.infer<typeof ApiConfigSchema>;
