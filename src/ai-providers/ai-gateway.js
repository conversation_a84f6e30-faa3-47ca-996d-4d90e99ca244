/**
 * src/ai-providers/ai-gateway.js
 *
 * 快手内部AI网关实现，用于调用快手内部的AI服务
 */
import fetch from 'node-fetch';
import { log } from '../../scripts/modules/utils.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 快手内部AI网关配置
const AI_GATEWAY_CONFIG = {
	baseUrl: 'https://ai-gateway.corp.kuaishou.com/v2/chat/completions',
	token: 'TXI1OjwcUJORWR',
	username: 'lijie14',
	provider: 'openai',
	format: 'openai'
};

/**
 * 下载图片并转换为base64格式
 * @param {string} url - 图片URL
 * @returns {Promise<string>} base64格式的图片数据
 */
// 支持本地图片路径和远程图片 URL 的 base64 转换
async function downloadImageAsBase64(url) {
	try {
		// 判断是否为本地文件路径
		if (!/^https?:\/\//.test(url) && !url.startsWith('data:')) {
			let imagePath = url;
			// 兼容 file:// 前缀
			if (imagePath.startsWith('file://')) {
				imagePath = imagePath.replace('file://', '');
			}
			// 读取本地图片
			if (!fs.existsSync(imagePath)) {
				throw new Error(`本地图片文件不存在: ${imagePath}`);
			}
			const imageBuffer = fs.readFileSync(imagePath);
			// 简单判断类型
			let mimeType = 'image/png';
			if (
				imagePath.toLowerCase().endsWith('.jpg') ||
				imagePath.toLowerCase().endsWith('.jpeg')
			) {
				mimeType = 'image/jpeg';
			} else if (imagePath.toLowerCase().endsWith('.gif')) {
				mimeType = 'image/gif';
			}
			const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
			return base64Image;
		}
		// 下载远程图片
		const response = await fetch(url);
		if (!response.ok) {
			throw new Error(
				`图片下载失败: ${response.status} ${response.statusText}`
			);
		}
		const imageBuffer = await response.buffer();
		let mimeType = response.headers.get('content-type') || 'image/jpeg';
		const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
		return base64Image;
	} catch (error) {
		log('error', `下载图片并转换为base64失败: ${error.message}`);
		throw error;
	}
}

/**
 * 转换消息格式，支持图片处理
 * @param {Array} messages - 消息数组
 * @returns {Promise<Array>} 转换后的消息数组
 */
// 自动将 user 消息中的 markdown 图片转为 image_url 消息
async function transformMessages(messages) {
	// 处理每个消息
	const processedMessagesPromises = messages.map(async (msg) => {
		// 只处理 role: 'user' 且 content 为 string 的情况
		if (msg.role === 'user' && typeof msg.content === 'string') {
			const contentStr = msg.content;
			const imageRegex = /!\[[^\]]*\]\(([^)]+)\)/g;
			let match;
			let lastIndex = 0;
			const contentArr = [];
			while ((match = imageRegex.exec(contentStr)) !== null) {
				// 前面的文本
				if (match.index > lastIndex) {
					const text = contentStr.slice(lastIndex, match.index);
					if (text.trim()) contentArr.push({ type: 'text', text });
				}
				// 图片url
				const url = match[1].trim();
				contentArr.push({ type: 'image_url', image_url: { url } });
				lastIndex = match.index + match[0].length;
			}
			// 剩余文本
			if (lastIndex < contentStr.length) {
				const text = contentStr.slice(lastIndex);
				if (text.trim()) contentArr.push({ type: 'text', text });
			}
			if (contentArr.length > 0) {
				msg = { ...msg, content: contentArr };
			}
		}

		// 如果消息内容是数组（可能包含图片）
		if (Array.isArray(msg.content)) {
			// 处理数组中的每个内容项
			const processedContentPromises = msg.content.map(async (item) => {
				// 如果是图片类型且包含url字段
				if (item.type === 'image_url' && item.image_url) {
					// 确保图片URL是base64格式
					if (item.image_url.url && !item.image_url.url.startsWith('data:')) {
						try {
							// 如果不是base64格式，记录警告并尝试处理
							log(
								'warn',
								`图片URL不是base64格式，尝试下载并转换: ${item.image_url.url.substring(0, 50)}...`
							);

							// 下载并转换图片为base64
							const base64Image = await downloadImageAsBase64(
								item.image_url.url
							);

							// 返回转换后的项
							return {
								type: 'image_url',
								image_url: {
									url: base64Image
								}
							};
						} catch (error) {
							log('error', `图片转换失败: ${error.message}`);
							// 如果转换失败，返回原始项并添加错误信息
							return {
								...item,
								_error: `图片转换失败: ${error.message}`
							};
						}
					}
				}
				// 返回原始项或已处理的项
				return item;
			});

			// 等待所有内容项处理完成
			const processedContent = await Promise.all(processedContentPromises);

			// 返回处理后的消息
			return {
				role: msg.role,
				content: processedContent
			};
		}

		// 如果消息内容是字符串，转换为文本类型的内容数组
		return {
			role: msg.role,
			content: [
				{
					type: 'text',
					text: msg.content
				}
			]
		};
	});

	// 等待所有消息处理完成
	return Promise.all(processedMessagesPromises);
}

/**
 * 生成文本响应
 *
 * @param {object} params - 参数对象
 * @param {string} params.modelId - 模型ID
 * @param {Array} params.messages - 消息数组
 * @param {number} [params.maxTokens] - 最大token数
 * @param {number} [params.temperature] - 温度参数
 * @returns {Promise<object>} 生成的文本和使用情况
 */
export async function generateAiGatewayText({
	modelId,
	messages,
	maxTokens,
	temperature
}) {
	log('debug', `使用快手AI网关生成文本，模型: ${modelId}`);

	try {
		// 转换消息格式（包括下载和转换图片为base64）
		const transformedMessages = await transformMessages(messages);

		// 构建请求体
		const requestBody = {
			model: 'claude-3-5-sonnet',
			stream: false,
			messages: transformedMessages
		};

		// 发送请求到快手内部AI网关
		const response = await fetch(AI_GATEWAY_CONFIG.baseUrl, {
			method: 'POST',
			timeout: 600000,
			headers: {
				'Content-Type': 'application/json',
				authorization: `Bearer ${AI_GATEWAY_CONFIG.token}`,
				'x-dmo-provider': AI_GATEWAY_CONFIG.format
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(
				`快手AI网关请求失败: ${response.status} ${response.statusText}`
			);
		}

		const data = await response.json();

		// 返回结果
		return {
			text: data.choices[0].message.content,
			usage: {
				inputTokens: data.usage?.prompt_tokens || 0,
				outputTokens: data.usage?.completion_tokens || 0
			}
		};
	} catch (error) {
		log('error', `AI网关生成文本失败: ${error.message}`);
		throw error;
	}
}

/**
 * 流式生成文本响应
 *
 * @param {object} params - 参数对象
 * @param {string} params.modelId - 模型ID
 * @param {Array} params.messages - 消息数组
 * @param {number} [params.maxTokens] - 最大token数
 * @param {number} [params.temperature] - 温度参数
 * @returns {Promise<object>} 流式响应对象
 */
export async function streamAiGatewayText({
	modelId,
	messages,
	maxTokens,
	temperature
}) {
	log('debug', `使用快手AI网关流式生成文本，模型: ${modelId}`);

	try {
		// 转换消息格式（包括下载和转换图片为base64）
		const transformedMessages = await transformMessages(messages);

		// 构建请求体
		const requestBody = {
			model: 'claude-3-5-sonnet',
			stream: true,
			messages: transformedMessages
			// ...(maxTokens && { max_tokens: maxTokens }),
			// ...(temperature && { temperature: temperature })
		};

		// 发送请求到快手内部AI网关
		const response = await fetch(AI_GATEWAY_CONFIG.baseUrl, {
			method: 'POST',
			timeout: 600000,
			headers: {
				'Content-Type': 'application/json',
				authorization: `Bearer ${AI_GATEWAY_CONFIG.token}`,
				'x-dmo-provider': AI_GATEWAY_CONFIG.format
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(
				`快手AI网关请求失败: ${response.status} ${response.statusText}`
			);
		}

		if (!response.body) {
			throw new Error('流式响应体为空');
		}

		// 创建一个流式响应的对象
		const reader = response.body.getReader();
		const decoder = new TextDecoder();

		// 模拟流式输出的接口
		const stream = {
			textStream: (async function* () {
				let buffer = '';
				let totalTokens = 0;

				try {
					while (true) {
						const { done, value } = await reader.read();
						if (done) break;

						const chunk = decoder.decode(value, { stream: true });
						buffer += chunk;

						// 处理SSE格式的数据
						const lines = buffer.split('\n');
						buffer = lines.pop() || '';

						for (const line of lines) {
							if (line.startsWith('data: ')) {
								const data = line.slice(6);
								if (data === '[DONE]') {
									break;
								}

								try {
									const parsed = JSON.parse(data);
									const content = parsed.choices[0]?.delta?.content || '';
									if (content) {
										totalTokens += 1; // 简单估算
										yield content;
									}
								} catch (e) {
									// 忽略解析错误
								}
							}
						}
					}
				} finally {
					// 确保读取器被释放
					reader.releaseLock();
				}
			})(),
			// 模拟usage信息
			usage: {
				promptTokens: 0, // 这个值很难准确估计
				completionTokens: 0 // 将在流式输出完成后更新
			}
		};

		return stream;
	} catch (error) {
		log('error', `AI网关流式生成文本失败: ${error.message}`);
		throw error;
	}
}

/**
 * 生成结构化对象
 *
 * @param {object} params - 参数对象
 * @param {string} params.modelId - 模型ID
 * @param {Array} params.messages - 消息数组
 * @param {object} params.schema - 对象模式定义
 * @param {string} params.objectName - 对象名称
 * @param {number} [params.maxTokens] - 最大token数
 * @param {number} [params.temperature] - 温度参数
 * @returns {Promise<object>} 生成的对象和使用情况
 */
export async function generateAiGatewayObject({
	modelId,
	messages,
	schema,
	objectName = 'generated_object',
	maxTokens,
	temperature
}) {
	log('debug', `使用快手AI网关生成对象 '${objectName}'，模型: ${modelId}`);

	try {
		// 转换消息格式（包括下载和转换图片为base64）
		const transformedMessages = await transformMessages(messages);

		// 构建系统提示，告诉模型需要生成特定格式的对象
		const systemMessage = {
			role: 'user',
			content: [
				{
					type: 'text',
					text: `You are a helpful assistant that generates structured data. Generate a ${objectName} based on the prompt. Your response should be valid JSON that strictly follows the provided schema.`
				}
			]
		};

		// 添加schema信息到用户消息中
		const schemaMessage = {
			role: 'user',
			content: [
				{
					type: 'text',
					text: `Generate a ${objectName} with the following schema: ${JSON.stringify(schema.shape || schema, null, 2)}`
				}
			]
		};

		// 构建完整消息列表
		const fullMessages = [systemMessage, ...transformedMessages, schemaMessage];

		// 构建请求体
		const requestBody = {
			model: 'claude-3-5-sonnet',
			stream: false,
			messages: fullMessages
			// ...(maxTokens && { max_tokens: maxTokens }),
			// ...(temperature && { temperature: temperature })
		};

		// 发送请求到快手内部AI网关
		const response = await fetch(AI_GATEWAY_CONFIG.baseUrl, {
			method: 'POST',
			timeout: 600000,
			headers: {
				'Content-Type': 'application/json',
				authorization: `Bearer ${AI_GATEWAY_CONFIG.token}`,
				'x-dmo-provider': AI_GATEWAY_CONFIG.format
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(
				`快手AI网关请求失败: ${response.status} ${response.statusText}`
			);
		}

		const data = await response.json();
		const responseText = data.choices[0].message.content;

		// 尝试从响应文本中提取JSON对象
		let objectResult;
		try {
			// 尝试直接解析
			objectResult = JSON.parse(responseText);
		} catch (e) {
			// 如果直接解析失败，尝试从文本中提取JSON部分
			const jsonMatch =
				responseText.match(/```json\s*([\s\S]+?)\s*```/) ||
				responseText.match(/\{[\s\S]*\}/);

			if (jsonMatch) {
				try {
					objectResult = JSON.parse(jsonMatch[1] || jsonMatch[0]);
				} catch (e2) {
					throw new Error(`无法解析AI生成的JSON对象: ${e2.message}`);
				}
			} else {
				throw new Error('AI响应中未找到有效的JSON对象');
			}
		}

		// 验证生成的对象是否符合schema
		let validatedObject;
		try {
			validatedObject = schema.parse(objectResult);
		} catch (validationError) {
			throw new Error(`生成的对象不符合schema: ${validationError.message}`);
		}

		// 返回结果
		return {
			object: validatedObject,
			usage: {
				inputTokens: data.usage?.prompt_tokens || 0,
				outputTokens: data.usage?.completion_tokens || 0
			}
		};
	} catch (error) {
		log('error', `AI网关生成对象失败: ${error.message}`);
		throw error;
	}
}
